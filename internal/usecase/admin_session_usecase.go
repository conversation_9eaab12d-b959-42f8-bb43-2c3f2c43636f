package usecase

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/repository"
)

type adminSessionUsecase struct {
	sessionRepo repository.SessionRepository
	userRepo    repository.UserRepository
	plateRepo   repository.PlateRepository
}

func NewAdminSessionUsecase(
	sessionRepo repository.SessionRepository,
	userRepo repository.UserRepository,
	plateRepo repository.PlateRepository,
) AdminSessionUsecase {
	return &adminSessionUsecase{
		sessionRepo: sessionRepo,
		userRepo:    userRepo,
		plateRepo:   plateRepo,
	}
}

func (uc *adminSessionUsecase) List(ctx context.Context, filters map[string]interface{}, limit, offset int) ([]*domain.Session, int, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}
	if offset < 0 {
		offset = 0
	}

	// Since there's no direct List method in session repository, we need to get sessions
	// through other means. Get all users first, then get their sessions
	users, err := uc.userRepo.List(ctx, 1000, 0)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get users for session listing: %w", err)
	}

	var sessions []*domain.Session
	for _, user := range users {
		userSessions, err := uc.sessionRepo.GetByUserID(ctx, user.ID, 1000, 0)
		if err != nil {
			// Continue with other users if one fails
			continue
		}
		sessions = append(sessions, userSessions...)
	}

	// Apply filters
	var filteredSessions []*domain.Session
	for _, session := range sessions {
		// Filter by status
		if status, ok := filters["status"].(domain.SessionStatus); ok && session.Status != status {
			continue
		}

		// Filter by user ID
		if userID, ok := filters["user_id"].(uuid.UUID); ok {
			if session.UserID == nil || *session.UserID != userID {
				continue
			}
		}

		// Filter by parking lot ID
		if parkingLotID, ok := filters["parking_lot_id"].(uuid.UUID); ok && session.ParkingLotID != parkingLotID {
			continue
		}

		// Filter by plate number
		if plateNumber, ok := filters["plate_number"].(string); ok {
			if session.PlateID != nil {
				plate, err := uc.plateRepo.GetByID(ctx, *session.PlateID)
				if err != nil || plate.PlateNumber != plateNumber {
					continue
				}
			} else {
				continue // Skip sessions without plate ID
			}
		}

		// Filter by date range
		if dateFrom, ok := filters["date_from"].(time.Time); ok && session.EntryTime.Before(dateFrom) {
			continue
		}
		if dateTo, ok := filters["date_to"].(time.Time); ok && session.EntryTime.After(dateTo) {
			continue
		}

		// Filter by paid status
		if isPaid, ok := filters["is_paid"].(bool); ok {
			sessionIsPaid := session.Amount != nil && *session.Amount > 0
			if sessionIsPaid != isPaid {
				continue
			}
		}

		filteredSessions = append(filteredSessions, session)
	}

	// Apply pagination to filtered results
	total := len(filteredSessions)
	start := offset
	end := offset + limit

	if start >= total {
		return []*domain.Session{}, total, nil
	}

	if end > total {
		end = total
	}

	paginatedSessions := filteredSessions[start:end]
	return paginatedSessions, total, nil
}

func (uc *adminSessionUsecase) GetByID(ctx context.Context, id uuid.UUID) (*domain.Session, error) {
	session, err := uc.sessionRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get session: %w", err)
	}
	return session, nil
}

func (uc *adminSessionUsecase) Update(ctx context.Context, id uuid.UUID, updates map[string]interface{}) (*domain.Session, error) {
	session, err := uc.sessionRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get session: %w", err)
	}

	// Apply updates
	if exitTime, ok := updates["exit_time"].(time.Time); ok {
		session.ExitTime = &exitTime
	}
	if amount, ok := updates["amount"].(int); ok {
		session.Amount = &amount
	}
	if discountAmount, ok := updates["discount_amount"].(int); ok {
		session.DiscountAmount = discountAmount
	}
	if errorMessage, ok := updates["error_message"].(string); ok {
		session.ErrorMessage = &errorMessage
	}

	if err := uc.sessionRepo.Update(ctx, session); err != nil {
		return nil, fmt.Errorf("failed to update session: %w", err)
	}

	return session, nil
}

func (uc *adminSessionUsecase) Complete(ctx context.Context, id uuid.UUID, exitTime *string, amount *int, reason string) (*domain.Session, error) {
	session, err := uc.sessionRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get session: %w", err)
	}

	if session.Status == domain.SessionStatusCompleted {
		return nil, fmt.Errorf("session is already completed")
	}

	// Set exit time
	var parsedExitTime time.Time
	if exitTime != nil {
		var parseErr error
		parsedExitTime, parseErr = time.Parse(time.RFC3339, *exitTime)
		if parseErr != nil {
			return nil, fmt.Errorf("invalid exit time format: %w", parseErr)
		}
	} else {
		parsedExitTime = time.Now()
	}

	session.ExitTime = &parsedExitTime

	// Set amount if provided
	if amount != nil {
		session.Amount = amount
	}

	// Complete the session
	if err := session.CompleteSession(parsedExitTime, *amount); err != nil {
		return nil, fmt.Errorf("failed to complete session: %w", err)
	}

	if err := uc.sessionRepo.Update(ctx, session); err != nil {
		return nil, fmt.Errorf("failed to complete session: %w", err)
	}

	// TODO: Add audit log for manual completion with reason

	return session, nil
}

func (uc *adminSessionUsecase) Cancel(ctx context.Context, id uuid.UUID, reason string) (*domain.Session, error) {
	session, err := uc.sessionRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get session: %w", err)
	}

	if session.Status == domain.SessionStatusCancelled {
		return nil, fmt.Errorf("session is already cancelled")
	}

	if session.Status == domain.SessionStatusCompleted {
		return nil, fmt.Errorf("cannot cancel completed session")
	}

	// Cancel the session
	session.CancelSession(reason)

	if err := uc.sessionRepo.Update(ctx, session); err != nil {
		return nil, fmt.Errorf("failed to cancel session: %w", err)
	}

	// TODO: Add audit log for cancellation with reason

	return session, nil
}
