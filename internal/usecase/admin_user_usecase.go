package usecase

import (
	"context"
	"fmt"
	"strings"

	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/repository"
)

type adminUserUsecase struct {
	userRepo repository.UserRepository
}

func NewAdminUserUsecase(userRepo repository.UserRepository) AdminUserUsecase {
	return &adminUserUsecase{
		userRepo: userRepo,
	}
}

func (uc *adminUserUsecase) List(ctx context.Context, status *domain.UserStatus, role *domain.UserRole, search *string, emailVerified *bool, limit, offset int) ([]*domain.User, int, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}
	if offset < 0 {
		offset = 0
	}

	// Get a larger set of users to apply filters
	users, err := uc.userRepo.List(ctx, 1000, 0)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list users: %w", err)
	}

	var filteredUsers []*domain.User
	for _, user := range users {
		// Skip deleted users
		if user.DeletedAt != nil {
			continue
		}

		// Filter by status
		if status != nil && user.Status != *status {
			continue
		}

		// Filter by role
		if role != nil && user.Role != *role {
			continue
		}

		// Filter by email verification
		if emailVerified != nil && user.EmailVerified != *emailVerified {
			continue
		}

		// Filter by search (username, email, or name)
		if search != nil && *search != "" {
			searchTerm := strings.ToLower(*search)
			if !strings.Contains(strings.ToLower(user.Username), searchTerm) &&
				!strings.Contains(strings.ToLower(user.Email), searchTerm) &&
				!strings.Contains(strings.ToLower(user.Name), searchTerm) {
				continue
			}
		}

		filteredUsers = append(filteredUsers, user)
	}

	total := len(filteredUsers)
	start := offset
	end := offset + limit

	if start >= total {
		return []*domain.User{}, total, nil
	}

	if end > total {
		end = total
	}

	paginatedUsers := filteredUsers[start:end]
	return paginatedUsers, total, nil
}

func (uc *adminUserUsecase) GetByID(ctx context.Context, id uuid.UUID) (*domain.User, error) {
	user, err := uc.userRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return user, nil
}

func (uc *adminUserUsecase) Update(ctx context.Context, id uuid.UUID, updates map[string]interface{}) (*domain.User, error) {
	user, err := uc.userRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	if username, ok := updates["username"].(string); ok && username != "" {
		existingUser, err := uc.userRepo.GetByUsername(ctx, username)
		if err == nil && existingUser.ID != user.ID {
			return nil, fmt.Errorf("username already exists")
		}
		user.Username = strings.ToLower(username)
	}

	if email, ok := updates["email"].(string); ok && email != "" {
		existingUser, err := uc.userRepo.GetByEmail(ctx, email)
		if err == nil && existingUser.ID != user.ID {
			return nil, fmt.Errorf("email already exists")
		}
		user.Email = strings.ToLower(email)
		user.EmailVerified = false
	}

	if name, ok := updates["name"].(string); ok && name != "" {
		user.Name = name
	}

	if phone, ok := updates["phone"].(string); ok {
		if phone == "" {
			user.Phone = nil
		} else {
			user.Phone = &phone
		}
	}

	if roleStr, ok := updates["role"].(string); ok {
		switch roleStr {
		case "user":
			user.Role = domain.UserRoleUser
		case "admin":
			user.Role = domain.UserRoleAdmin
		default:
			return nil, fmt.Errorf("invalid role: %s", roleStr)
		}
	}

	if statusStr, ok := updates["status"].(string); ok {
		switch statusStr {
		case "active":
			user.Activate()
		case "suspended":
			user.Suspend()
		default:
			return nil, fmt.Errorf("invalid status: %s", statusStr)
		}
	}

	if emailVerified, ok := updates["email_verified"].(bool); ok {
		user.EmailVerified = emailVerified
	}

	if autoPaymentEnabled, ok := updates["auto_payment_enabled"].(bool); ok {
		user.AutoPaymentEnabled = autoPaymentEnabled
	}

	if err := uc.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	return user, nil
}

func (uc *adminUserUsecase) Suspend(ctx context.Context, id uuid.UUID, reason string) error {
	user, err := uc.userRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}

	if user.Status == domain.UserStatusSuspended {
		return fmt.Errorf("user is already suspended")
	}

	user.Suspend()

	if err := uc.userRepo.Update(ctx, user); err != nil {
		return fmt.Errorf("failed to suspend user: %w", err)
	}

	// TODO: Add audit log for suspension with reason
	// TODO: Send notification to user about suspension

	return nil
}

func (uc *adminUserUsecase) Activate(ctx context.Context, id uuid.UUID) error {
	user, err := uc.userRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}

	if user.Status == domain.UserStatusActive {
		return fmt.Errorf("user is already active")
	}

	user.Activate()

	if err := uc.userRepo.Update(ctx, user); err != nil {
		return fmt.Errorf("failed to activate user: %w", err)
	}

	// TODO: Add audit log for activation
	// TODO: Send notification to user about activation

	return nil
}
