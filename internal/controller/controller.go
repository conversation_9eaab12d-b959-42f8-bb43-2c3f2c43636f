package controller

import (
	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime/types"

	api "github.com/smooth-inc/backend/api/generated"
	"github.com/smooth-inc/backend/internal/infra/logger"
	"github.com/smooth-inc/backend/internal/usecase"
)

type Controller struct {
	authController         *AuthController
	userController         *UserController
	parkingLotController   *ParkingLotController
	sessionController      *SessionController
	paymentController      *PaymentController
	bookingController      *BookingController
	notificationController *NotificationController
	hardwareController     *HardwareController
	adminController        *AdminController
}

func NewController(
	authUsecase usecase.AuthUsecase,
	userUsecase usecase.UserUsecase,
	plateUsecase usecase.PlateUsecase,
	parkingLotUsecase usecase.ParkingLotUsecase,
	sessionUsecase usecase.SessionUsecase,
	paymentUsecase usecase.PaymentUsecase,
	paymentMethodUsecase usecase.PaymentMethodUsecase,
	webhookUsecase usecase.WebhookUsecase,
	bookingUsecase usecase.BookingUsecase,
	notificationUsecase usecase.NotificationUsecase,
	hardwareUsecase usecase.HardwareUsecase,
	adminParkingLotUsecase usecase.AdminParkingLotUsecase,
	adminParkingLotConfigUsecase usecase.AdminParkingLotConfigUsecase,
	adminUserUsecase usecase.AdminUserUsecase,
	adminPlateUsecase usecase.AdminPlateUsecase,
	adminSessionUsecase usecase.AdminSessionUsecase,
	adminPaymentUsecase usecase.AdminPaymentUsecase,
	adminAnalyticsUsecase usecase.AdminAnalyticsUsecase,
	logger *logger.Logger,
) *Controller {
	return &Controller{
		authController:         NewAuthController(authUsecase, logger),
		userController:         NewUserController(userUsecase, plateUsecase, logger),
		parkingLotController:   NewParkingLotController(parkingLotUsecase, adminParkingLotConfigUsecase, logger),
		sessionController:      NewSessionController(sessionUsecase, logger),
		paymentController:      NewPaymentController(paymentUsecase, paymentMethodUsecase, webhookUsecase, logger),
		bookingController:      NewBookingController(bookingUsecase, logger),
		notificationController: NewNotificationController(notificationUsecase, logger),
		hardwareController:     NewHardwareController(hardwareUsecase, logger),
		adminController: NewAdminController(
			adminParkingLotUsecase,
			adminParkingLotConfigUsecase,
			adminUserUsecase,
			adminPlateUsecase,
			adminSessionUsecase,
			adminPaymentUsecase,
			adminAnalyticsUsecase,
			logger,
		),
	}
}

func (ctrl *Controller) PostAuthLogin(c *gin.Context)    { ctrl.authController.PostAuthLogin(c) }
func (ctrl *Controller) PostAuthRefresh(c *gin.Context)  { ctrl.authController.PostAuthRefresh(c) }
func (ctrl *Controller) PostAuthRegister(c *gin.Context) { ctrl.authController.PostAuthRegister(c) }
func (ctrl *Controller) PostAuthForgotPassword(c *gin.Context) {
	ctrl.authController.PostAuthForgotPassword(c)
}
func (ctrl *Controller) PostAuthResetPassword(c *gin.Context) {
	ctrl.authController.PostAuthResetPassword(c)
}
func (ctrl *Controller) GetAuthVerifyEmail(c *gin.Context, params api.GetAuthVerifyEmailParams) {
	ctrl.authController.GetAuthVerifyEmail(c, params)
}
func (ctrl *Controller) PostAuthResendVerification(c *gin.Context) {
	ctrl.authController.PostAuthResendVerification(c)
}
func (ctrl *Controller) PostAuthChangePassword(c *gin.Context) {
	ctrl.authController.PostAuthChangePassword(c)
}

func (ctrl *Controller) GetAuthSessions(c *gin.Context) {
	ctrl.authController.GetAuthSessions(c)
}
func (ctrl *Controller) DeleteAuthSessionsSessionId(c *gin.Context, sessionId types.UUID) {
	ctrl.authController.DeleteAuthSessionsSessionId(c, sessionId)
}
func (ctrl *Controller) PostAuthSessionsLogoutAll(c *gin.Context) {
	ctrl.authController.PostAuthSessionsLogoutAll(c)
}

func (ctrl *Controller) GetBookings(c *gin.Context, params api.GetBookingsParams) {
	ctrl.bookingController.GetBookings(c, params)
}
func (ctrl *Controller) PostBookings(c *gin.Context) { ctrl.bookingController.PostBookings(c) }
func (ctrl *Controller) DeleteBookingsBookingId(c *gin.Context, bookingId types.UUID) {
	ctrl.bookingController.DeleteBookingsBookingId(c, bookingId)
}
func (ctrl *Controller) GetBookingsBookingId(c *gin.Context, bookingId types.UUID) {
	ctrl.bookingController.GetBookingsBookingId(c, bookingId)
}

func (ctrl *Controller) PostHardwareDetection(c *gin.Context) {
	ctrl.hardwareController.PostHardwareDetection(c)
}

func (ctrl *Controller) GetNotifications(c *gin.Context, params api.GetNotificationsParams) {
	ctrl.notificationController.GetNotifications(c, params)
}
func (ctrl *Controller) PatchNotificationsNotificationIdMarkRead(c *gin.Context, notificationId types.UUID) {
	ctrl.notificationController.PatchNotificationsNotificationIdMarkRead(c, notificationId)
}

func (ctrl *Controller) GetParkingLots(c *gin.Context, params api.GetParkingLotsParams) {
	ctrl.parkingLotController.GetParkingLots(c, params)
}
func (ctrl *Controller) GetParkingLotsLotId(c *gin.Context, lotId types.UUID) {
	ctrl.parkingLotController.GetParkingLotsLotId(c, lotId)
}
func (ctrl *Controller) GetParkingLotsLotIdAvailability(c *gin.Context, lotId types.UUID) {
	ctrl.parkingLotController.GetParkingLotsLotIdAvailability(c, lotId)
}

func (ctrl *Controller) GetPayments(c *gin.Context, params api.GetPaymentsParams) {
	ctrl.paymentController.GetPayments(c, params)
}
func (ctrl *Controller) PostPaymentsWebhooksStripe(c *gin.Context) {
	ctrl.paymentController.PostPaymentsWebhooksStripe(c)
}
func (ctrl *Controller) PostPaymentsSessionIdCreatePaymentLink(c *gin.Context, sessionId types.UUID) {
	ctrl.paymentController.PostPaymentsSessionIdCreatePaymentLink(c, sessionId)
}
func (ctrl *Controller) PostPaymentsSessionIdProcessAutoPayment(c *gin.Context, sessionId types.UUID) {
	ctrl.paymentController.PostPaymentsSessionIdProcessAutoPayment(c, sessionId)
}

func (ctrl *Controller) GetPaymentMethods(c *gin.Context) {
	ctrl.paymentController.GetPaymentMethods(c)
}
func (ctrl *Controller) PostPaymentMethods(c *gin.Context) {
	ctrl.paymentController.PostPaymentMethods(c)
}
func (ctrl *Controller) DeletePaymentMethodsPaymentMethodId(c *gin.Context, paymentMethodId string) {
	ctrl.paymentController.DeletePaymentMethodsPaymentMethodId(c, paymentMethodId)
}
func (ctrl *Controller) PostPaymentMethodsPaymentMethodIdSetDefault(c *gin.Context, paymentMethodId string) {
	ctrl.paymentController.PostPaymentMethodsPaymentMethodIdSetDefault(c, paymentMethodId)
}
func (ctrl *Controller) PostPaymentMethodsStripeCallback(c *gin.Context) {
	ctrl.paymentController.PostPaymentMethodsStripeCallback(c)
}
func (ctrl *Controller) GetPaymentMethodsValidateSetup(c *gin.Context) {
	ctrl.paymentController.GetPaymentMethodsValidateSetup(c)
}

func (ctrl *Controller) GetSessions(c *gin.Context, params api.GetSessionsParams) {
	ctrl.sessionController.GetSessions(c, params)
}
func (ctrl *Controller) GetSessionsActive(c *gin.Context) {
	ctrl.sessionController.GetSessionsActive(c)
}
func (ctrl *Controller) GetSessionsSessionId(c *gin.Context, sessionId types.UUID) {
	ctrl.sessionController.GetSessionsSessionId(c, sessionId)
}

func (ctrl *Controller) GetUsersProfile(c *gin.Context) { ctrl.userController.GetUsersProfile(c) }
func (ctrl *Controller) PutUsersProfile(c *gin.Context) { ctrl.userController.PutUsersProfile(c) }

func (ctrl *Controller) GetUsersPlates(c *gin.Context)  { ctrl.userController.GetUsersPlates(c) }
func (ctrl *Controller) PostUsersPlates(c *gin.Context) { ctrl.userController.PostUsersPlates(c) }
func (ctrl *Controller) DeleteUsersPlatesPlateId(c *gin.Context, plateId types.UUID) {
	ctrl.userController.DeleteUsersPlatesPlateId(c, plateId)
}

// Admin Parking Lot Management
func (ctrl *Controller) GetAdminParkingLots(c *gin.Context, params api.GetAdminParkingLotsParams) {
	ctrl.adminController.GetAdminParkingLots(c, params)
}
func (ctrl *Controller) PostAdminParkingLots(c *gin.Context) {
	ctrl.adminController.PostAdminParkingLots(c)
}
func (ctrl *Controller) GetAdminParkingLotsLotId(c *gin.Context, lotId types.UUID) {
	ctrl.adminController.GetAdminParkingLotsLotId(c, lotId)
}
func (ctrl *Controller) PutAdminParkingLotsLotId(c *gin.Context, lotId types.UUID) {
	ctrl.adminController.PutAdminParkingLotsLotId(c, lotId)
}
func (ctrl *Controller) DeleteAdminParkingLotsLotId(c *gin.Context, lotId types.UUID) {
	ctrl.adminController.DeleteAdminParkingLotsLotId(c, lotId)
}

// Admin Parking Lot Pricing Config Management
func (ctrl *Controller) GetAdminParkingLotsLotIdPricingConfigs(c *gin.Context, lotId types.UUID, params api.GetAdminParkingLotsLotIdPricingConfigsParams) {
	ctrl.adminController.GetAdminParkingLotsLotIdPricingConfigs(c, lotId, params)
}
func (ctrl *Controller) PostAdminParkingLotsLotIdPricingConfigs(c *gin.Context, lotId types.UUID) {
	ctrl.adminController.PostAdminParkingLotsLotIdPricingConfigs(c, lotId)
}
func (ctrl *Controller) DeleteAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context, lotId types.UUID, configId types.UUID) {
	ctrl.adminController.DeleteAdminParkingLotsLotIdPricingConfigsConfigId(c, lotId, configId)
}
func (ctrl *Controller) GetAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context, lotId types.UUID, configId types.UUID) {
	ctrl.adminController.GetAdminParkingLotsLotIdPricingConfigsConfigId(c, lotId, configId)
}
func (ctrl *Controller) PutAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context, lotId types.UUID, configId types.UUID) {
	ctrl.adminController.PutAdminParkingLotsLotIdPricingConfigsConfigId(c, lotId, configId)
}
func (ctrl *Controller) PostAdminParkingLotsLotIdPricingConfigsConfigIdActivate(c *gin.Context, lotId types.UUID, configId types.UUID) {
	ctrl.adminController.PostAdminParkingLotsLotIdPricingConfigsConfigIdActivate(c, lotId, configId)
}
func (ctrl *Controller) PostAdminPricingConfigsCalculatePreview(c *gin.Context) {
	ctrl.adminController.PostAdminPricingConfigsCalculatePreview(c)
}
func (ctrl *Controller) PostAdminPricingConfigsValidate(c *gin.Context) {
	ctrl.adminController.PostAdminPricingConfigsValidate(c)
}

func (ctrl *Controller) GetAdminUsers(c *gin.Context, params api.GetAdminUsersParams) {
	ctrl.adminController.GetAdminUsers(c, params)
}
func (ctrl *Controller) GetAdminUsersUserId(c *gin.Context, userId types.UUID) {
	ctrl.adminController.GetAdminUsersUserId(c, userId)
}
func (ctrl *Controller) PutAdminUsersUserId(c *gin.Context, userId types.UUID) {
	ctrl.adminController.PutAdminUsersUserId(c, userId)
}
func (ctrl *Controller) PostAdminUsersUserIdSuspend(c *gin.Context, userId types.UUID) {
	ctrl.adminController.PostAdminUsersUserIdSuspend(c, userId)
}
func (ctrl *Controller) PostAdminUsersUserIdActivate(c *gin.Context, userId types.UUID) {
	ctrl.adminController.PostAdminUsersUserIdActivate(c, userId)
}

func (ctrl *Controller) GetAdminAnalyticsDashboard(c *gin.Context, params api.GetAdminAnalyticsDashboardParams) {
	ctrl.adminController.GetAdminAnalyticsDashboard(c, params)
}
func (ctrl *Controller) GetAdminAnalyticsRevenue(c *gin.Context, params api.GetAdminAnalyticsRevenueParams) {
	ctrl.adminController.GetAdminAnalyticsRevenue(c, params)
}
func (ctrl *Controller) GetAdminAnalyticsUsage(c *gin.Context, params api.GetAdminAnalyticsUsageParams) {
	ctrl.adminController.GetAdminAnalyticsUsage(c, params)
}

func (ctrl *Controller) GetAdminPayments(c *gin.Context, params api.GetAdminPaymentsParams) {
	ctrl.adminController.GetAdminPayments(c, params)
}
func (ctrl *Controller) GetAdminPaymentsPaymentId(c *gin.Context, paymentId types.UUID) {
	ctrl.adminController.GetAdminPaymentsPaymentId(c, paymentId)
}
func (ctrl *Controller) PostAdminPaymentsPaymentIdRefund(c *gin.Context, paymentId types.UUID) {
	ctrl.adminController.PostAdminPaymentsPaymentIdRefund(c, paymentId)
}

func (ctrl *Controller) GetAdminPlates(c *gin.Context, params api.GetAdminPlatesParams) {
	ctrl.adminController.GetAdminPlates(c, params)
}
func (ctrl *Controller) GetAdminPlatesPlateId(c *gin.Context, plateId types.UUID) {
	ctrl.adminController.GetAdminPlatesPlateId(c, plateId)
}
func (ctrl *Controller) DeleteAdminPlatesPlateId(c *gin.Context, plateId types.UUID) {
	ctrl.adminController.DeleteAdminPlatesPlateId(c, plateId)
}
func (ctrl *Controller) PostAdminPlatesPlateIdActivate(c *gin.Context, plateId types.UUID) {
	ctrl.adminController.PostAdminPlatesPlateIdActivate(c, plateId)
}
func (ctrl *Controller) PostAdminPlatesPlateIdDeactivate(c *gin.Context, plateId types.UUID) {
	ctrl.adminController.PostAdminPlatesPlateIdDeactivate(c, plateId)
}

func (ctrl *Controller) PostAdminReportsExport(c *gin.Context) {
	ctrl.adminController.PostAdminReportsExport(c)
}

func (ctrl *Controller) GetAdminSessions(c *gin.Context, params api.GetAdminSessionsParams) {
	ctrl.adminController.GetAdminSessions(c, params)
}
func (ctrl *Controller) GetAdminSessionsSessionId(c *gin.Context, sessionId types.UUID) {
	ctrl.adminController.GetAdminSessionsSessionId(c, sessionId)
}
func (ctrl *Controller) PutAdminSessionsSessionId(c *gin.Context, sessionId types.UUID) {
	ctrl.adminController.PutAdminSessionsSessionId(c, sessionId)
}
func (ctrl *Controller) PostAdminSessionsSessionIdCancel(c *gin.Context, sessionId types.UUID) {
	ctrl.adminController.PostAdminSessionsSessionIdCancel(c, sessionId)
}
func (ctrl *Controller) PostAdminSessionsSessionIdComplete(c *gin.Context, sessionId types.UUID) {
	ctrl.adminController.PostAdminSessionsSessionIdComplete(c, sessionId)
}
