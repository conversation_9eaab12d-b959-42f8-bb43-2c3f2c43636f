package controller

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/oapi-codegen/runtime/types"

	api "github.com/smooth-inc/backend/api/generated"
	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/infra/http/response"
	"github.com/smooth-inc/backend/internal/infra/logger"
	"github.com/smooth-inc/backend/internal/usecase"
)

type AdminController struct {
	adminParkingLotUsecase       usecase.AdminParkingLotUsecase
	adminParkingLotConfigUsecase usecase.AdminParkingLotConfigUsecase
	adminUserUsecase             usecase.AdminUserUsecase
	adminPlateUsecase            usecase.AdminPlateUsecase
	adminSessionUsecase          usecase.AdminSessionUsecase
	adminPaymentUsecase          usecase.AdminPaymentUsecase
	adminAnalyticsUsecase        usecase.AdminAnalyticsUsecase
	logger                       *logger.Logger
}

func NewAdminController(
	adminParkingLotUsecase usecase.AdminParkingLotUsecase,
	adminParkingLotConfigUsecase usecase.AdminParkingLotConfigUsecase,
	adminUserUsecase usecase.AdminUserUsecase,
	adminPlateUsecase usecase.AdminPlateUsecase,
	adminSessionUsecase usecase.AdminSessionUsecase,
	adminPaymentUsecase usecase.AdminPaymentUsecase,
	adminAnalyticsUsecase usecase.AdminAnalyticsUsecase,
	logger *logger.Logger,
) *AdminController {
	return &AdminController{
		adminParkingLotUsecase:       adminParkingLotUsecase,
		adminParkingLotConfigUsecase: adminParkingLotConfigUsecase,
		adminUserUsecase:             adminUserUsecase,
		adminPlateUsecase:            adminPlateUsecase,
		adminSessionUsecase:          adminSessionUsecase,
		adminPaymentUsecase:          adminPaymentUsecase,
		adminAnalyticsUsecase:        adminAnalyticsUsecase,
		logger:                       logger,
	}
}

// Admin Parking Lot Management
func (ac *AdminController) GetAdminParkingLots(c *gin.Context, params api.GetAdminParkingLotsParams) {
	ac.logger.LogInfo(c.Request.Context(), "Admin getting parking lots", map[string]interface{}{
		"method": "GET",
		"path":   "/admin/parking-lots",
	})

	limit := 20
	offset := 0
	if params.Limit != nil {
		limit = *params.Limit
	}
	if params.Offset != nil {
		offset = *params.Offset
	}

	var status *domain.LotStatus
	if params.Status != nil {
		lotStatus := domain.LotStatus(*params.Status)
		status = &lotStatus
	}

	lots, total, err := ac.adminParkingLotUsecase.List(c.Request.Context(), status, params.Search, limit, offset)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to get parking lots")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to get parking lots")
		return
	}

	// Convert to API response format
	apiLots := make([]api.AdminParkingLotSummary, len(lots))
	for i, lot := range lots {
		lotID := types.UUID(lot.ID)
		apiLots[i] = api.AdminParkingLotSummary{
			Id:          &lotID,
			Name:        &lot.Name,
			Address:     &lot.Address,
			TotalSpots:  &lot.TotalSpots,
			Status:      (*api.LotStatus)(&lot.Status),
			CreatedAt:   &lot.CreatedAt,
		}
	}

	apiResponse := api.AdminParkingLotsResponse{
		Lots:   &apiLots,
		Total:  &total,
		Limit:  &limit,
		Offset: &offset,
	}

	response.Success(c, apiResponse)
}

func (ac *AdminController) PostAdminParkingLots(c *gin.Context) {
	ac.logger.LogInfo(c.Request.Context(), "Admin creating parking lot", map[string]interface{}{
		"method": "POST",
		"path":   "/admin/parking-lots",
	})

	var req api.CreateParkingLotRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", err.Error())
		return
	}

	// Validate required fields
	if req.Name == "" {
		response.BadRequest(c, "VALIDATION_ERROR", "Name is required", nil)
		return
	}
	if req.Address == "" {
		response.BadRequest(c, "VALIDATION_ERROR", "Address is required", nil)
		return
	}
	if req.TotalSpots <= 0 {
		response.BadRequest(c, "VALIDATION_ERROR", "Total spots must be greater than 0", nil)
		return
	}
	if req.HourlyRate != nil && *req.HourlyRate < 0 {
		response.BadRequest(c, "VALIDATION_ERROR", "Hourly rate cannot be negative", nil)
		return
	}

	var features []string
	if req.Features != nil {
		features = *req.Features
	}

	hourlyRate := 0
	if req.HourlyRate != nil {
		hourlyRate = *req.HourlyRate
	}

	lot, err := ac.adminParkingLotUsecase.Create(
		c.Request.Context(),
		req.Name,
		req.Address,
		req.Latitude,
		req.Longitude,
		req.TotalSpots,
		hourlyRate,
		features,
	)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to create parking lot")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to create parking lot")
		return
	}

	// Convert to API response format
	apiLot := convertParkingLotToAPI(lot)
	response.Created(c, apiLot)
}

func (ac *AdminController) GetAdminParkingLotsLotId(c *gin.Context, lotId types.UUID) {
	ac.logger.LogInfo(c.Request.Context(), "Admin getting parking lot details", map[string]interface{}{
		"method": "GET",
		"path":   "/admin/parking-lots/{lotId}",
		"lot_id": lotId,
	})

	lot, stats, err := ac.adminParkingLotUsecase.GetWithStats(c.Request.Context(), uuid.UUID(lotId))
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to get parking lot")
		response.NotFound(c, "NOT_FOUND", "Parking lot not found")
		return
	}

	// Convert to API response format with stats
	apiLot := convertParkingLotToAdminDetails(lot, stats)
	response.Success(c, apiLot)
}

func (ac *AdminController) PutAdminParkingLotsLotId(c *gin.Context, lotId types.UUID) {
	ac.logger.LogInfo(c.Request.Context(), "Admin updating parking lot", map[string]interface{}{
		"method": "PUT",
		"path":   "/admin/parking-lots/{lotId}",
		"lot_id": lotId,
	})

	var req api.UpdateParkingLotRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", err.Error())
		return
	}

	// Convert request to updates map
	updates := make(map[string]interface{})
	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Address != nil {
		updates["address"] = *req.Address
	}
	if req.Latitude != nil {
		updates["latitude"] = *req.Latitude
	}
	if req.Longitude != nil {
		updates["longitude"] = *req.Longitude
	}
	if req.TotalSpots != nil {
		updates["total_spots"] = *req.TotalSpots
	}
	if req.HeightLimitCm != nil {
		updates["height_limit_cm"] = *req.HeightLimitCm
	}
	if req.HourlyRate != nil {
		updates["hourly_rate"] = *req.HourlyRate
	}
	if req.DailyMaxRate != nil {
		updates["daily_max_rate"] = *req.DailyMaxRate
	}
	if req.FreeMinutes != nil {
		updates["free_minutes"] = *req.FreeMinutes
	}
	if req.Is24h != nil {
		updates["is_24h"] = *req.Is24h
	}
	if req.OpenTime != nil {
		updates["open_time"] = *req.OpenTime
	}
	if req.CloseTime != nil {
		updates["close_time"] = *req.CloseTime
	}
	if req.Features != nil {
		updates["features"] = *req.Features
	}
	if req.Status != nil {
		updates["status"] = string(*req.Status)
	}
	if req.OperatorName != nil {
		updates["operator_name"] = *req.OperatorName
	}
	if req.ContactPhone != nil {
		updates["contact_phone"] = *req.ContactPhone
	}

	lot, err := ac.adminParkingLotUsecase.Update(c.Request.Context(), uuid.UUID(lotId), updates)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to update parking lot")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to update parking lot")
		return
	}

	// Convert to API response format
	apiLot := convertParkingLotToAPI(lot)
	response.Success(c, apiLot)
}

func (ac *AdminController) DeleteAdminParkingLotsLotId(c *gin.Context, lotId types.UUID) {
	ac.logger.LogInfo(c.Request.Context(), "Admin deleting parking lot", map[string]interface{}{
		"method": "DELETE",
		"path":   "/admin/parking-lots/{lotId}",
		"lot_id": lotId,
	})

	err := ac.adminParkingLotUsecase.Delete(c.Request.Context(), uuid.UUID(lotId))
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to delete parking lot")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to delete parking lot")
		return
	}

	response.NoContent(c)
}

// Helper functions
func convertParkingLotToAPI(lot *domain.ParkingLot) api.ParkingLot {
	// Convert uuid.UUID to types.UUID
	lotID := types.UUID(lot.ID)

	return api.ParkingLot{
		Id:            &lotID,
		Name:          &lot.Name,
		Address:       &lot.Address,
		Latitude:      &lot.Latitude,
		Longitude:     &lot.Longitude,
		TotalSpots:    &lot.TotalSpots,
		HeightLimitCm: lot.HeightLimitCm,
		HourlyRate:    &lot.HourlyRate,
		DailyMaxRate:  lot.DailyMaxRate,
		FreeMinutes:   &lot.FreeMinutes,
		Is24h:         &lot.Is24h,
		OpenTime:      lot.OpenTime,
		CloseTime:     lot.CloseTime,
		Features:      &lot.Features,
		Images:        &lot.Images,
		Status:        (*api.LotStatus)(&lot.Status),
		OperatorName:  lot.OperatorName,
		ContactPhone:  lot.ContactPhone,
		CreatedAt:     &lot.CreatedAt,
		UpdatedAt:     &lot.UpdatedAt,
	}
}

func convertParkingLotToAdminDetails(lot *domain.ParkingLot, stats map[string]interface{}) api.AdminParkingLotDetails {
	// Convert uuid.UUID to types.UUID
	lotID := types.UUID(lot.ID)

	details := api.AdminParkingLotDetails{
		Address:       &lot.Address,
		CloseTime:     lot.CloseTime,
		ContactPhone:  lot.ContactPhone,
		CreatedAt:     &lot.CreatedAt,
		DailyMaxRate:  lot.DailyMaxRate,
		Features:      &lot.Features,
		FreeMinutes:   &lot.FreeMinutes,
		HeightLimitCm: lot.HeightLimitCm,
		HourlyRate:    &lot.HourlyRate,
		Id:            &lotID,
		Images:        &lot.Images,
		Is24h:         &lot.Is24h,
		Latitude:      &lot.Latitude,
		Longitude:     &lot.Longitude,
		Name:          &lot.Name,
		OpenTime:      lot.OpenTime,
		OperatorName:  lot.OperatorName,
		Status:        (*api.LotStatus)(&lot.Status),
		TotalSpots:    &lot.TotalSpots,
		UpdatedAt:     &lot.UpdatedAt,
	}

	// Add stats if available - these are part of AdminParkingLotDetails
	if totalRevenue, ok := stats["today_revenue"].(int64); ok {
		revenue := int(totalRevenue)
		details.TotalRevenue = &revenue
	}
	if avgDuration, ok := stats["average_session_duration"].(int64); ok {
		duration := int(avgDuration)
		details.AverageSessionDuration = &duration
	}

	return details
}

// Admin Plate Management
func (ac *AdminController) GetAdminPlates(c *gin.Context, params api.GetAdminPlatesParams) {
	ac.logger.LogInfo(c.Request.Context(), "Admin getting plates", map[string]interface{}{
		"method": "GET",
		"path":   "/admin/plates",
	})

	limit := 20
	offset := 0
	if params.Limit != nil {
		limit = *params.Limit
	}
	if params.Offset != nil {
		offset = *params.Offset
	}

	var plateType *domain.PlateType
	if params.PlateType != nil {
		pt := domain.PlateType(*params.PlateType)
		plateType = &pt
	}

	plates, total, err := ac.adminPlateUsecase.List(c.Request.Context(), params.UserId, params.PlateNumber, plateType, params.IsActive, limit, offset)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to get plates")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to get plates")
		return
	}

	// Convert to API response format
	apiPlates := make([]api.AdminPlateSummary, len(plates))
	for i, plate := range plates {
		plateID := types.UUID(plate.ID)
		userID := types.UUID(plate.UserID)
		apiPlates[i] = api.AdminPlateSummary{
			Id:          &plateID,
			PlateNumber: &plate.PlateNumber,
			PlateType:   (*api.PlateType)(&plate.PlateType),
			UserId:      &userID,
			IsActive:    &plate.IsActive,
			CreatedAt:   &plate.CreatedAt,
		}
	}

	apiResponse := api.AdminPlatesResponse{
		Plates: &apiPlates,
		Total:  &total,
		Limit:  &limit,
		Offset: &offset,
	}

	response.Success(c, apiResponse)
}

func (ac *AdminController) GetAdminPlatesPlateId(c *gin.Context, plateId types.UUID) {
	ac.logger.LogInfo(c.Request.Context(), "Admin getting plate details", map[string]interface{}{
		"method":   "GET",
		"path":     "/admin/plates/{plateId}",
		"plate_id": plateId,
	})

	plate, err := ac.adminPlateUsecase.GetByID(c.Request.Context(), uuid.UUID(plateId))
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to get plate")
		response.NotFound(c, "NOT_FOUND", "Plate not found")
		return
	}

	// Convert to API response format
	apiPlate := convertPlateToAdminDetails(plate)
	response.Success(c, apiPlate)
}

func (ac *AdminController) DeleteAdminPlatesPlateId(c *gin.Context, plateId types.UUID) {
	ac.logger.LogInfo(c.Request.Context(), "Admin deleting plate", map[string]interface{}{
		"method":   "DELETE",
		"path":     "/admin/plates/{plateId}",
		"plate_id": plateId,
	})

	err := ac.adminPlateUsecase.Delete(c.Request.Context(), uuid.UUID(plateId))
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to delete plate")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to delete plate")
		return
	}

	response.NoContent(c)
}

func (ac *AdminController) PostAdminPlatesPlateIdActivate(c *gin.Context, plateId types.UUID) {
	ac.logger.LogInfo(c.Request.Context(), "Admin activating plate", map[string]interface{}{
		"method":   "POST",
		"path":     "/admin/plates/{plateId}/activate",
		"plate_id": plateId,
	})

	err := ac.adminPlateUsecase.Activate(c.Request.Context(), uuid.UUID(plateId))
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to activate plate")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to activate plate")
		return
	}

	response.Success(c, map[string]interface{}{
		"message": "Plate activated successfully",
		"success": true,
	})
}

func (ac *AdminController) PostAdminPlatesPlateIdDeactivate(c *gin.Context, plateId types.UUID) {
	ac.logger.LogInfo(c.Request.Context(), "Admin deactivating plate", map[string]interface{}{
		"method":   "POST",
		"path":     "/admin/plates/{plateId}/deactivate",
		"plate_id": plateId,
	})

	err := ac.adminPlateUsecase.Deactivate(c.Request.Context(), uuid.UUID(plateId))
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to deactivate plate")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to deactivate plate")
		return
	}

	response.Success(c, map[string]interface{}{
		"message": "Plate deactivated successfully",
		"success": true,
	})
}

// Admin User Management
func (ac *AdminController) GetAdminUsers(c *gin.Context, params api.GetAdminUsersParams) {
	ac.logger.LogInfo(c.Request.Context(), "Admin getting users", map[string]interface{}{
		"method": "GET",
		"path":   "/admin/users",
	})

	limit := 20
	offset := 0
	if params.Limit != nil {
		limit = *params.Limit
	}
	if params.Offset != nil {
		offset = *params.Offset
	}

	var status *domain.UserStatus
	if params.Status != nil {
		userStatus := domain.UserStatus(*params.Status)
		status = &userStatus
	}

	var role *domain.UserRole
	if params.Role != nil {
		userRole := domain.UserRole(*params.Role)
		role = &userRole
	}

	users, total, err := ac.adminUserUsecase.List(c.Request.Context(), status, role, params.Search, params.EmailVerified, limit, offset)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to get users")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to get users")
		return
	}

	// Convert to API response format
	apiUsers := make([]api.AdminUserSummary, len(users))
	for i, user := range users {
		userID := types.UUID(user.ID)
		apiUsers[i] = api.AdminUserSummary{
			Id:            &userID,
			Username:      &user.Username,
			Email:         &user.Email,
			Name:          &user.Name,
			Role:          (*api.UserRole)(&user.Role),
			Status:        (*api.UserStatus)(&user.Status),
			EmailVerified: &user.EmailVerified,
			CreatedAt:     &user.CreatedAt,
			LastLoginAt:   user.LastLoginAt,
		}
	}

	apiResponse := api.AdminUsersResponse{
		Users:  &apiUsers,
		Total:  &total,
		Limit:  &limit,
		Offset: &offset,
	}

	response.Success(c, apiResponse)
}

func (ac *AdminController) GetAdminUsersUserId(c *gin.Context, userId types.UUID) {
	ac.logger.LogInfo(c.Request.Context(), "Admin getting user details", map[string]interface{}{
		"method":  "GET",
		"path":    "/admin/users/{userId}",
		"user_id": userId,
	})

	user, err := ac.adminUserUsecase.GetByID(c.Request.Context(), uuid.UUID(userId))
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to get user")
		response.NotFound(c, "NOT_FOUND", "User not found")
		return
	}

	// Convert to API response format
	apiUser := convertUserToAdminDetails(user)
	response.Success(c, apiUser)
}

func (ac *AdminController) PutAdminUsersUserId(c *gin.Context, userId types.UUID) {
	ac.logger.LogInfo(c.Request.Context(), "Admin updating user", map[string]interface{}{
		"method":  "PUT",
		"path":    "/admin/users/{userId}",
		"user_id": userId,
	})

	var req api.AdminUpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", err.Error())
		return
	}

	// Convert request to updates map
	updates := make(map[string]interface{})
	if req.Username != nil {
		updates["username"] = *req.Username
	}
	if req.Email != nil {
		updates["email"] = *req.Email
	}
	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Phone != nil {
		updates["phone"] = *req.Phone
	}
	if req.Role != nil {
		updates["role"] = string(*req.Role)
	}
	if req.Status != nil {
		updates["status"] = string(*req.Status)
	}
	if req.EmailVerified != nil {
		updates["email_verified"] = *req.EmailVerified
	}
	if req.AutoPaymentEnabled != nil {
		updates["auto_payment_enabled"] = *req.AutoPaymentEnabled
	}

	user, err := ac.adminUserUsecase.Update(c.Request.Context(), uuid.UUID(userId), updates)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to update user")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to update user")
		return
	}

	// Convert to API response format
	apiUser := convertUserToAPI(user)
	response.Success(c, apiUser)
}

func (ac *AdminController) PostAdminUsersUserIdSuspend(c *gin.Context, userId types.UUID) {
	ac.logger.LogInfo(c.Request.Context(), "Admin suspending user", map[string]interface{}{
		"method":  "POST",
		"path":    "/admin/users/{userId}/suspend",
		"user_id": userId,
	})

	var req struct {
		Reason *string `json:"reason"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", err.Error())
		return
	}

	reason := ""
	if req.Reason != nil {
		reason = *req.Reason
	}

	err := ac.adminUserUsecase.Suspend(c.Request.Context(), uuid.UUID(userId), reason)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to suspend user")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to suspend user")
		return
	}

	response.Success(c, map[string]interface{}{
		"message": "User suspended successfully",
		"success": true,
	})
}

func (ac *AdminController) PostAdminUsersUserIdActivate(c *gin.Context, userId types.UUID) {
	ac.logger.LogInfo(c.Request.Context(), "Admin activating user", map[string]interface{}{
		"method":  "POST",
		"path":    "/admin/users/{userId}/activate",
		"user_id": userId,
	})

	err := ac.adminUserUsecase.Activate(c.Request.Context(), uuid.UUID(userId))
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to activate user")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to activate user")
		return
	}

	response.Success(c, map[string]interface{}{
		"message": "User activated successfully",
		"success": true,
	})
}

// Helper functions for user conversion
func convertUserToAPI(user *domain.User) api.User {
	// Convert uuid.UUID to types.UUID and email to types.Email
	userID := types.UUID(user.ID)
	email := types.Email(user.Email)

	return api.User{
		Id:                     &userID,
		Username:               &user.Username,
		Email:                  &email,
		Name:                   &user.Name,
		Phone:                  user.Phone,
		PreferredLanguage:      (*api.UserPreferredLanguage)(&user.PreferredLanguage),
		Role:                   (*api.UserRole)(&user.Role),
		Status:                 (*api.UserStatus)(&user.Status),
		DefaultPaymentMethodId: user.DefaultPaymentMethodID,
		AutoPaymentEnabled:     &user.AutoPaymentEnabled,
		NotifyEmail:            &user.NotifyEmail,
		NotifyPush:             &user.NotifyPush,
		EmailVerified:          &user.EmailVerified,
		LastLoginAt:            user.LastLoginAt,
		CreatedAt:              &user.CreatedAt,
		UpdatedAt:              &user.UpdatedAt,
	}
}

func convertUserToAdminDetails(user *domain.User) api.AdminUserDetails {
	// Convert uuid.UUID to types.UUID
	userID := types.UUID(user.ID)

	return api.AdminUserDetails{
		Id:                &userID,
		Username:          &user.Username,
		Email:             &user.Email,
		Name:              &user.Name,
		Phone:             user.Phone,
		Role:              (*api.UserRole)(&user.Role),
		Status:            (*api.UserStatus)(&user.Status),
		StripeCustomerId:  user.StripeCustomerID,
		AutoPaymentEnabled: &user.AutoPaymentEnabled,
		EmailVerified:     &user.EmailVerified,
		LastLoginAt:       user.LastLoginAt,
		CreatedAt:         &user.CreatedAt,
		UpdatedAt:         &user.UpdatedAt,
	}
}

// Helper function for plate conversion
func convertPlateToAdminDetails(plate *domain.Plate) api.AdminPlateDetails {
	// Convert uuid.UUID to types.UUID
	plateID := types.UUID(plate.ID)
	userID := types.UUID(plate.UserID)

	return api.AdminPlateDetails{
		Id:             &plateID,
		UserId:         &userID,
		Region:         &plate.Region,
		Classification: &plate.Classification,
		Hiragana:       &plate.Hiragana,
		SerialNumber:   &plate.SerialNumber,
		PlateNumber:    &plate.PlateNumber,
		PlateType:      (*api.PlateType)(&plate.PlateType),
		IsActive:       &plate.IsActive,
		CreatedAt:      &plate.CreatedAt,
		UpdatedAt:      &plate.UpdatedAt,
	}
}

// Admin Session Management
func (ac *AdminController) GetAdminSessions(c *gin.Context, params api.GetAdminSessionsParams) {
	ac.logger.LogInfo(c.Request.Context(), "Admin getting sessions", map[string]interface{}{
		"method": "GET",
		"path":   "/admin/sessions",
	})

	limit := 20
	offset := 0
	if params.Limit != nil {
		limit = *params.Limit
	}
	if params.Offset != nil {
		offset = *params.Offset
	}

	filters := make(map[string]interface{})
	if params.Status != nil {
		filters["status"] = string(*params.Status)
	}
	if params.UserId != nil {
		filters["user_id"] = *params.UserId
	}
	if params.ParkingLotId != nil {
		filters["parking_lot_id"] = *params.ParkingLotId
	}
	// Note: PlateId parameter not available in GetAdminSessionsParams
	if params.DateFrom != nil {
		filters["date_from"] = *params.DateFrom
	}
	if params.DateTo != nil {
		filters["date_to"] = *params.DateTo
	}

	sessions, total, err := ac.adminSessionUsecase.List(c.Request.Context(), filters, limit, offset)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to get sessions")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to get sessions")
		return
	}

	apiSessions := make([]api.AdminSessionSummary, len(sessions))
	for i, session := range sessions {
		apiSessions[i] = convertSessionToAdminSummary(session)
	}

	apiResponse := api.AdminSessionsResponse{
		Sessions: &apiSessions,
		Total:    &total,
		Limit:    &limit,
		Offset:   &offset,
	}

	response.Success(c, apiResponse)
}

func (ac *AdminController) GetAdminSessionsSessionId(c *gin.Context, sessionId types.UUID) {
	ac.logger.LogInfo(c.Request.Context(), "Admin getting session details", map[string]interface{}{
		"method":     "GET",
		"path":       "/admin/sessions/{sessionId}",
		"session_id": sessionId,
	})

	session, err := ac.adminSessionUsecase.GetByID(c.Request.Context(), uuid.UUID(sessionId))
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to get session")
		response.NotFound(c, "NOT_FOUND", "Session not found")
		return
	}

	apiSession := convertSessionToAPI(session)
	response.Success(c, apiSession)
}

func (ac *AdminController) PutAdminSessionsSessionId(c *gin.Context, sessionId types.UUID) {
	ac.logger.LogInfo(c.Request.Context(), "Admin updating session", map[string]interface{}{
		"method":     "PUT",
		"path":       "/admin/sessions/{sessionId}",
		"session_id": sessionId,
	})

	var req api.AdminUpdateSessionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", err.Error())
		return
	}

	updates := make(map[string]interface{})
	if req.Status != nil {
		updates["status"] = string(*req.Status)
	}
	if req.ExitTime != nil {
		updates["exit_time"] = *req.ExitTime
	}
	if req.Amount != nil {
		updates["amount"] = *req.Amount
	}

	session, err := ac.adminSessionUsecase.Update(c.Request.Context(), uuid.UUID(sessionId), updates)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to update session")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to update session")
		return
	}

	apiSession := convertSessionToAPI(session)
	response.Success(c, apiSession)
}

func (ac *AdminController) PostAdminSessionsSessionIdComplete(c *gin.Context, sessionId types.UUID) {
	ac.logger.LogInfo(c.Request.Context(), "Admin completing session", map[string]interface{}{
		"method":     "POST",
		"path":       "/admin/sessions/{sessionId}/complete",
		"session_id": sessionId,
	})

	var req api.PostAdminSessionsSessionIdCompleteJSONBody
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", err.Error())
		return
	}

	var exitTimeStr *string
	if req.ExitTime != nil {
		timeStr := req.ExitTime.Format(time.RFC3339)
		exitTimeStr = &timeStr
	}

	var reason string
	if req.Reason != nil {
		reason = *req.Reason
	}

	session, err := ac.adminSessionUsecase.Complete(c.Request.Context(), uuid.UUID(sessionId), exitTimeStr, req.Amount, reason)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to complete session")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to complete session")
		return
	}

	apiSession := convertSessionToAPI(session)
	response.Success(c, apiSession)
}

func (ac *AdminController) PostAdminSessionsSessionIdCancel(c *gin.Context, sessionId types.UUID) {
	ac.logger.LogInfo(c.Request.Context(), "Admin canceling session", map[string]interface{}{
		"method":     "POST",
		"path":       "/admin/sessions/{sessionId}/cancel",
		"session_id": sessionId,
	})

	var req api.PostAdminSessionsSessionIdCancelJSONBody
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", err.Error())
		return
	}

	session, err := ac.adminSessionUsecase.Cancel(c.Request.Context(), uuid.UUID(sessionId), req.Reason)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to cancel session")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to cancel session")
		return
	}

	apiSession := convertSessionToAPI(session)
	response.Success(c, apiSession)
}

// Admin Payment Management
func (ac *AdminController) GetAdminPayments(c *gin.Context, params api.GetAdminPaymentsParams) {
	ac.logger.LogInfo(c.Request.Context(), "Admin getting payments", map[string]interface{}{
		"method": "GET",
		"path":   "/admin/payments",
	})

	limit := 20
	offset := 0
	if params.Limit != nil {
		limit = *params.Limit
	}
	if params.Offset != nil {
		offset = *params.Offset
	}

	filters := make(map[string]interface{})
	if params.Status != nil {
		filters["status"] = string(*params.Status)
	}
	if params.UserId != nil {
		filters["user_id"] = *params.UserId
	}
	if params.SessionId != nil {
		filters["session_id"] = *params.SessionId
	}
	if params.DateFrom != nil {
		filters["date_from"] = *params.DateFrom
	}
	if params.DateTo != nil {
		filters["date_to"] = *params.DateTo
	}
	if params.MinAmount != nil {
		filters["amount_min"] = *params.MinAmount
	}
	if params.MaxAmount != nil {
		filters["amount_max"] = *params.MaxAmount
	}

	payments, total, err := ac.adminPaymentUsecase.List(c.Request.Context(), filters, limit, offset)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to get payments")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to get payments")
		return
	}

	apiPayments := make([]api.AdminPaymentSummary, len(payments))
	for i, payment := range payments {
		apiPayments[i] = convertPaymentToAdminSummary(payment)
	}

	apiResponse := api.AdminPaymentsResponse{
		Payments: &apiPayments,
		Total:    &total,
		Limit:    &limit,
		Offset:   &offset,
	}

	response.Success(c, apiResponse)
}

func (ac *AdminController) GetAdminPaymentsPaymentId(c *gin.Context, paymentId types.UUID) {
	ac.logger.LogInfo(c.Request.Context(), "Admin getting payment details", map[string]interface{}{
		"method":     "GET",
		"path":       "/admin/payments/{paymentId}",
		"payment_id": paymentId,
	})

	payment, err := ac.adminPaymentUsecase.GetByID(c.Request.Context(), uuid.UUID(paymentId))
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to get payment")
		response.NotFound(c, "NOT_FOUND", "Payment not found")
		return
	}

	apiPayment := convertPaymentToAPI(payment)
	response.Success(c, apiPayment)
}

func (ac *AdminController) PostAdminPaymentsPaymentIdRefund(c *gin.Context, paymentId types.UUID) {
	ac.logger.LogInfo(c.Request.Context(), "Admin processing refund", map[string]interface{}{
		"method":     "POST",
		"path":       "/admin/payments/{paymentId}/refund",
		"payment_id": paymentId,
	})

	var req api.PostAdminPaymentsPaymentIdRefundJSONBody
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", err.Error())
		return
	}

	payment, err := ac.adminPaymentUsecase.ProcessRefund(c.Request.Context(), uuid.UUID(paymentId), req.Amount, req.Reason)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to process refund")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to process refund")
		return
	}

	apiPayment := convertPaymentToAPI(payment)
	response.Success(c, apiPayment)
}

// Admin Analytics
func (ac *AdminController) GetAdminAnalyticsDashboard(c *gin.Context, params api.GetAdminAnalyticsDashboardParams) {
	ac.logger.LogInfo(c.Request.Context(), "Admin getting dashboard analytics", map[string]interface{}{
		"method": "GET",
		"path":   "/admin/analytics/dashboard",
	})

	period := "7d"
	if params.Period != nil {
		period = string(*params.Period)
	}

	analytics, err := ac.adminAnalyticsUsecase.GetDashboardAnalytics(c.Request.Context(), period)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to get dashboard analytics")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to get dashboard analytics")
		return
	}

	response.Success(c, analytics)
}

func (ac *AdminController) GetAdminAnalyticsRevenue(c *gin.Context, params api.GetAdminAnalyticsRevenueParams) {
	ac.logger.LogInfo(c.Request.Context(), "Admin getting revenue analytics", map[string]interface{}{
		"method": "GET",
		"path":   "/admin/analytics/revenue",
	})

	period := "7d"
	if params.Period != nil {
		period = string(*params.Period)
	}

	var dateFromStr, dateToStr *string
	if params.DateFrom != nil {
		str := params.DateFrom.String()
		dateFromStr = &str
	}
	if params.DateTo != nil {
		str := params.DateTo.String()
		dateToStr = &str
	}

	analytics, err := ac.adminAnalyticsUsecase.GetRevenueAnalytics(c.Request.Context(), period, dateFromStr, dateToStr)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to get revenue analytics")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to get revenue analytics")
		return
	}

	response.Success(c, analytics)
}

func (ac *AdminController) GetAdminAnalyticsUsage(c *gin.Context, params api.GetAdminAnalyticsUsageParams) {
	ac.logger.LogInfo(c.Request.Context(), "Admin getting usage analytics", map[string]interface{}{
		"method": "GET",
		"path":   "/admin/analytics/usage",
	})

	period := "7d"
	if params.Period != nil {
		period = string(*params.Period)
	}

	var dateFromStr2, dateToStr2 *string
	if params.DateFrom != nil {
		str := params.DateFrom.String()
		dateFromStr2 = &str
	}
	if params.DateTo != nil {
		str := params.DateTo.String()
		dateToStr2 = &str
	}

	analytics, err := ac.adminAnalyticsUsecase.GetUsageAnalytics(c.Request.Context(), period, dateFromStr2, dateToStr2)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to get usage analytics")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to get usage analytics")
		return
	}

	response.Success(c, analytics)
}

func (ac *AdminController) PostAdminReportsExport(c *gin.Context) {
	ac.logger.LogInfo(c.Request.Context(), "Admin exporting report", map[string]interface{}{
		"method": "POST",
		"path":   "/admin/reports/export",
	})

	var req api.ExportReportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", err.Error())
		return
	}

	filters := make(map[string]interface{})
	if req.DateFrom != nil {
		filters["date_from"] = *req.DateFrom
	}
	if req.DateTo != nil {
		filters["date_to"] = *req.DateTo
	}
	if req.Filters != nil {
		if req.Filters.ParkingLotIds != nil {
			filters["parking_lot_ids"] = *req.Filters.ParkingLotIds
		}
		if req.Filters.UserIds != nil {
			filters["user_ids"] = *req.Filters.UserIds
		}
	}

	report, err := ac.adminAnalyticsUsecase.ExportReport(c.Request.Context(), string(req.ReportType), string(req.Format), filters)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to export report")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to export report")
		return
	}

	response.Success(c, report)
}

// Helper functions for session and payment conversion
func convertSessionToAPI(session *domain.Session) api.Session {
	// Convert uuid.UUID to types.UUID
	sessionID := types.UUID(session.ID)
	parkingLotID := types.UUID(session.ParkingLotID)

	var userID *types.UUID
	if session.UserID != nil {
		uid := types.UUID(*session.UserID)
		userID = &uid
	}

	var plateID *types.UUID
	if session.PlateID != nil {
		pid := types.UUID(*session.PlateID)
		plateID = &pid
	}

	return api.Session{
		Id:           &sessionID,
		UserId:       userID,
		PlateId:      plateID,
		ParkingLotId: &parkingLotID,
		EntryTime:    &session.EntryTime,
		ExitTime:     session.ExitTime,
		Amount:       session.Amount,
		Status:       (*api.SessionStatus)(&session.Status),
		CreatedAt:    &session.CreatedAt,
		UpdatedAt:    &session.UpdatedAt,
	}
}

func convertPaymentToAPI(payment *domain.Payment) api.Payment {
	// Convert uuid.UUID to types.UUID
	paymentID := types.UUID(payment.ID)
	userID := types.UUID(payment.UserID)
	sessionID := types.UUID(payment.SessionID)

	return api.Payment{
		Id:                    &paymentID,
		UserId:                &userID,
		SessionId:             &sessionID,
		Amount:                &payment.Amount,
		Currency:              &payment.Currency,
		Status:                (*api.PaymentStatus)(&payment.Status),
		CardBrand:             payment.CardBrand,
		CardLast4:             payment.CardLast4,
		StripePaymentIntentId: payment.StripePaymentIntentID,
		CreatedAt:             &payment.CreatedAt,
		UpdatedAt:             &payment.UpdatedAt,
	}
}

func convertSessionToAdminSummary(session *domain.Session) api.AdminSessionSummary {
	// Convert uuid.UUID to types.UUID
	sessionID := types.UUID(session.ID)

	var userID *types.UUID
	if session.UserID != nil {
		uid := types.UUID(*session.UserID)
		userID = &uid
	}

	return api.AdminSessionSummary{
		Id:              &sessionID,
		UserId:          userID,
		EntryTime:       &session.EntryTime,
		ExitTime:        session.ExitTime,
		DurationMinutes: session.DurationMinutes,
		Amount:          session.Amount,
		Status:          (*api.SessionStatus)(&session.Status),
		CreatedAt:       &session.CreatedAt,
	}
}

func convertPaymentToAdminSummary(payment *domain.Payment) api.AdminPaymentSummary {
	// Convert uuid.UUID to types.UUID
	paymentID := types.UUID(payment.ID)
	userID := types.UUID(payment.UserID)
	sessionID := types.UUID(payment.SessionID)

	return api.AdminPaymentSummary{
		Id:                    &paymentID,
		UserId:                &userID,
		SessionId:             &sessionID,
		Amount:                &payment.Amount,
		Status:                (*api.PaymentStatus)(&payment.Status),
		StripePaymentIntentId: payment.StripePaymentIntentID,
		CreatedAt:             &payment.CreatedAt,
	}
}

// Admin Parking Lot Configuration Management
func (ac *AdminController) GetAdminParkingLotsLotIdPricingConfigs(c *gin.Context, lotId types.UUID, params api.GetAdminParkingLotsLotIdPricingConfigsParams) {
	ac.logger.LogInfo(c.Request.Context(), "Admin getting parking lot pricing configs", map[string]interface{}{
		"method": "GET",
		"path":   "/admin/parking-lots/{lotId}/pricing-configs",
		"lot_id": lotId,
	})

	includeInactive := false
	if params.IncludeInactive != nil {
		includeInactive = *params.IncludeInactive
	}

	configs, err := ac.adminParkingLotConfigUsecase.GetByParkingLotID(c.Request.Context(), uuid.UUID(lotId), includeInactive)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to get parking lot configs")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to get parking lot configs")
		return
	}

	apiConfigs := make([]api.ParkingLotConfig, len(configs))
	for i, config := range configs {
		apiConfigs[i] = convertParkingLotConfigToAPI(config)
	}

	response.Success(c, apiConfigs)
}

func (ac *AdminController) PostAdminParkingLotsLotIdPricingConfigs(c *gin.Context, lotId types.UUID) {
	ac.logger.LogInfo(c.Request.Context(), "Admin creating parking lot pricing config", map[string]interface{}{
		"method": "POST",
		"path":   "/admin/parking-lots/{lotId}/pricing-configs",
		"lot_id": lotId,
	})

	var req api.CreatePricingConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", err.Error())
		return
	}

	// Get user ID from context (admin user creating the config)
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "UNAUTHORIZED", "User not authenticated")
		return
	}

	createdBy, ok := userID.(uuid.UUID)
	if !ok {
		response.InternalServerError(c, "INTERNAL_ERROR", "Invalid user ID format")
		return
	}

	// Convert API pricing rules to domain pricing rules
	pricingRules := convertAPIPricingRulesToDomain(req.PricingRules)

	config, err := ac.adminParkingLotConfigUsecase.Create(c.Request.Context(), uuid.UUID(lotId), req.ConfigName, pricingRules, createdBy)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to create parking lot config")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to create parking lot config")
		return
	}

	apiConfig := convertParkingLotConfigToAPI(config)
	response.Created(c, apiConfig)
}

func (ac *AdminController) GetAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context, lotId types.UUID, configId types.UUID) {
	ac.logger.LogInfo(c.Request.Context(), "Admin getting parking lot pricing config", map[string]interface{}{
		"method":    "GET",
		"path":      "/admin/parking-lots/{lotId}/pricing-configs/{configId}",
		"lot_id":    lotId,
		"config_id": configId,
	})

	config, err := ac.adminParkingLotConfigUsecase.GetByID(c.Request.Context(), uuid.UUID(configId))
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to get parking lot config")
		response.NotFound(c, "NOT_FOUND", "Parking lot config not found")
		return
	}

	// Verify config belongs to the specified parking lot
	if config.ParkingLotID != uuid.UUID(lotId) {
		response.NotFound(c, "NOT_FOUND", "Config not found for this parking lot")
		return
	}

	apiConfig := convertParkingLotConfigToAPI(config)
	response.Success(c, apiConfig)
}

func (ac *AdminController) PutAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context, lotId types.UUID, configId types.UUID) {
	ac.logger.LogInfo(c.Request.Context(), "Admin updating parking lot pricing config", map[string]interface{}{
		"method":    "PUT",
		"path":      "/admin/parking-lots/{lotId}/pricing-configs/{configId}",
		"lot_id":    lotId,
		"config_id": configId,
	})

	var req api.UpdatePricingConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", err.Error())
		return
	}

	var configName *string
	var pricingRules *domain.PricingRules

	if req.ConfigName != nil {
		configName = req.ConfigName
	}

	if req.PricingRules != nil {
		rules := convertAPIPricingRulesToDomain(*req.PricingRules)
		pricingRules = &rules
	}

	config, err := ac.adminParkingLotConfigUsecase.Update(c.Request.Context(), uuid.UUID(configId), configName, pricingRules)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to update parking lot config")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to update parking lot config")
		return
	}

	// Verify config belongs to the specified parking lot
	if config.ParkingLotID != uuid.UUID(lotId) {
		response.NotFound(c, "NOT_FOUND", "Config not found for this parking lot")
		return
	}

	apiConfig := convertParkingLotConfigToAPI(config)
	response.Success(c, apiConfig)
}

func (ac *AdminController) DeleteAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context, lotId types.UUID, configId types.UUID) {
	ac.logger.LogInfo(c.Request.Context(), "Admin deleting parking lot pricing config", map[string]interface{}{
		"method":    "DELETE",
		"path":      "/admin/parking-lots/{lotId}/pricing-configs/{configId}",
		"lot_id":    lotId,
		"config_id": configId,
	})

	err := ac.adminParkingLotConfigUsecase.Delete(c.Request.Context(), uuid.UUID(configId))
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to delete parking lot config")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to delete parking lot config")
		return
	}

	response.NoContent(c)
}

func (ac *AdminController) PostAdminParkingLotsLotIdPricingConfigsConfigIdActivate(c *gin.Context, lotId types.UUID, configId types.UUID) {
	ac.logger.LogInfo(c.Request.Context(), "Admin activating parking lot pricing config", map[string]interface{}{
		"method":    "POST",
		"path":      "/admin/parking-lots/{lotId}/pricing-configs/{configId}/activate",
		"lot_id":    lotId,
		"config_id": configId,
	})

	var req api.ActivatePricingConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", err.Error())
		return
	}

	var effectiveUntil *time.Time
	if req.EffectiveUntil != nil {
		effectiveUntil = req.EffectiveUntil
	}

	config, err := ac.adminParkingLotConfigUsecase.Activate(c.Request.Context(), uuid.UUID(configId), req.EffectiveFrom, effectiveUntil)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to activate parking lot config")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to activate parking lot config")
		return
	}

	// Verify config belongs to the specified parking lot
	if config.ParkingLotID != uuid.UUID(lotId) {
		response.NotFound(c, "NOT_FOUND", "Config not found for this parking lot")
		return
	}

	apiConfig := convertParkingLotConfigToAPI(config)
	response.Success(c, apiConfig)
}

// Admin Pricing Configuration Validation and Preview
func (ac *AdminController) PostAdminPricingConfigsValidate(c *gin.Context) {
	ac.logger.LogInfo(c.Request.Context(), "Admin validating pricing configuration", map[string]interface{}{
		"method": "POST",
		"path":   "/admin/pricing-configs/validate",
	})

	var req api.PricingRules
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", err.Error())
		return
	}

	pricingRules := convertAPIPricingRulesToDomain(req)

	valid, errors, warnings, err := ac.adminParkingLotConfigUsecase.ValidatePricingRules(c.Request.Context(), pricingRules)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to validate pricing rules")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to validate pricing rules")
		return
	}

	validationResponse := api.ValidationResponse{
		Valid:    &valid,
		Errors:   &errors,
		Warnings: &warnings,
	}

	response.Success(c, validationResponse)
}

func (ac *AdminController) PostAdminPricingConfigsCalculatePreview(c *gin.Context) {
	ac.logger.LogInfo(c.Request.Context(), "Admin calculating fee preview", map[string]interface{}{
		"method": "POST",
		"path":   "/admin/pricing-configs/calculate-preview",
	})

	var req api.FeeCalculationPreviewRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", err.Error())
		return
	}

	pricingRules := convertAPIPricingRulesToDomain(req.PricingRules)

	// Convert API scenarios to domain scenarios
	scenarios := make([]usecase.ParkingScenario, len(req.Scenarios))
	for i, scenario := range req.Scenarios {
		scenarios[i] = usecase.ParkingScenario{
			Name:      scenario.Name,
			EntryTime: scenario.EntryTime,
			ExitTime:  scenario.ExitTime,
		}
	}

	results, err := ac.adminParkingLotConfigUsecase.CalculatePreview(c.Request.Context(), pricingRules, scenarios)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to calculate fee preview")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to calculate fee preview")
		return
	}

	// Convert results to API format
	apiResults := make([]api.ScenarioResult, len(results))
	for i, result := range results {
		apiResults[i] = api.ScenarioResult{
			Name: &result.Name,
			Fee:  &result.Fee,
			Breakdown: &api.FeeBreakdown{
				BaseFee:         &result.Breakdown.BaseFee,
				NightCapApplied: &result.Breakdown.NightCapApplied,
				DailyCapApplied: &result.Breakdown.DailyCapApplied,
				OverrideFee:     &result.Breakdown.OverrideFee,
				DiscountAmount:  &result.Breakdown.DiscountAmount,
				FinalFee:        &result.Breakdown.FinalFee,
			},
			AppliedRules:    &result.AppliedRules,
			FreeMinutesUsed: &result.FreeMinutesUsed,
			TotalMinutes:    &result.TotalMinutes,
			BillableMinutes: &result.BillableMinutes,
		}
	}

	previewResponse := api.FeeCalculationPreviewResponse{
		Scenarios: &apiResults,
	}

	response.Success(c, previewResponse)
}


